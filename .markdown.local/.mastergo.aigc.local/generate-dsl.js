/**
 * MasterGo DSL 生成脚本
 * 根据DSL类型说明文档调用MCP生成图纸的DSL代码
 */

const fs = require('fs');
const path = require('path');

/**
 * 生成DSL代码的主函数
 * @param {Object} options - 配置选项
 * @param {string} options.shortLink - MasterGo短链接
 * @param {string} options.fileId - MasterGo文件ID
 * @param {string} options.layerId - MasterGo图层ID
 * @param {string} options.pageName - 页面名称
 * @param {string} options.componentName - 组件名称
 */
async function generateDSL(options) {
  const { shortLink, fileId, layerId, pageName, componentName = 'index' } = options;
  
  // 验证输入参数
  if (!shortLink && (!fileId || !layerId)) {
    throw new Error('必须提供shortLink或者fileId和layerId');
  }
  
  if (!pageName) {
    throw new Error('必须提供pageName');
  }
  
  try {
    // 调用MCP工具获取DSL
    let dslData;
    if (shortLink) {
      console.log(`正在从短链接获取DSL: ${shortLink}`);
      // 这里应该调用 mcp__getDsl_mastergo-magic-mcp({ shortLink })
      // 由于这是示例脚本，我们模拟返回数据
      dslData = await mockGetDslFromShortLink(shortLink);
    } else {
      console.log(`正在从fileId和layerId获取DSL: ${fileId}, ${layerId}`);
      // 这里应该调用 mcp__getDsl_mastergo-magic-mcp({ fileId, layerId })
      dslData = await mockGetDslFromIds(fileId, layerId);
    }
    
    // 创建目标目录
    const targetDir = path.join(
      '.markdown.local/.mastergo.aigc.local/pages',
      pageName,
      componentName
    );
    
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
      console.log(`创建目录: ${targetDir}`);
    }
    
    // 保存DSL数据
    const dslFilePath = path.join(targetDir, 'dsl.json');
    fs.writeFileSync(dslFilePath, JSON.stringify(dslData, null, 2));
    console.log(`DSL数据已保存到: ${dslFilePath}`);
    
    // 生成TypeScript类型文件
    const tsFilePath = path.join(targetDir, 'dsl.d.ts');
    const tsContent = generateTypeScriptDefinition(dslData);
    fs.writeFileSync(tsFilePath, tsContent);
    console.log(`TypeScript定义已保存到: ${tsFilePath}`);
    
    // 生成README文档
    const readmePath = path.join(targetDir, 'README.md');
    const readmeContent = generateReadme(pageName, componentName, dslData);
    fs.writeFileSync(readmePath, readmeContent);
    console.log(`文档已保存到: ${readmePath}`);
    
    return {
      success: true,
      dslPath: dslFilePath,
      tsPath: tsFilePath,
      readmePath: readmePath
    };
    
  } catch (error) {
    console.error('生成DSL失败:', error.message);
    throw error;
  }
}

/**
 * 模拟从短链接获取DSL数据
 */
async function mockGetDslFromShortLink(shortLink) {
  // 这里应该是实际的MCP调用
  return {
    styles: {
      "paint_primary": {
        token: "primary-color",
        value: ["#1890ff"]
      },
      "font_body": {
        token: "body-font",
        value: {
          family: "PingFang SC",
          size: 14,
          style: "normal",
          decoration: "none",
          case: "none",
          lineHeight: "1.5",
          letterSpacing: "0"
        }
      }
    },
    nodes: [
      {
        id: "frame_1",
        name: "主容器",
        type: "FRAME",
        layoutStyle: {
          width: 375,
          height: 667,
          relativeX: 0,
          relativeY: 0
        },
        children: []
      }
    ],
    components: []
  };
}

/**
 * 模拟从fileId和layerId获取DSL数据
 */
async function mockGetDslFromIds(fileId, layerId) {
  return mockGetDslFromShortLink(`mock://${fileId}/${layerId}`);
}

/**
 * 生成TypeScript类型定义
 */
function generateTypeScriptDefinition(dslData) {
  return `/**
 * 自动生成的DSL类型定义
 * 基于 MasterCodeSchema.Dsl 类型
 */

import { MasterCodeSchema } from '../../../code-schema';

export type GeneratedDsl = MasterCodeSchema.Dsl;

export const dslData: GeneratedDsl = ${JSON.stringify(dslData, null, 2)};
`;
}

/**
 * 生成README文档
 */
function generateReadme(pageName, componentName, dslData) {
  const nodeCount = dslData.nodes?.length || 0;
  const componentCount = dslData.components?.length || 0;
  const styleCount = Object.keys(dslData.styles || {}).length;
  
  return `# ${pageName}/${componentName} DSL 文档

## 概述

此文档包含从MasterGo设计文件生成的DSL代码。

## 统计信息

- 节点数量: ${nodeCount}
- 组件数量: ${componentCount}  
- 样式数量: ${styleCount}

## 文件说明

- \`dsl.json\` - DSL数据文件
- \`dsl.d.ts\` - TypeScript类型定义
- \`README.md\` - 本文档

## 使用方法

\`\`\`typescript
import { dslData } from './dsl.d.ts';

// 使用DSL数据进行渲染
console.log(dslData);
\`\`\`

## 生成时间

${new Date().toISOString()}
`;
}

module.exports = {
  generateDSL
};

// 如果直接运行此脚本
if (require.main === module) {
  // 示例用法
  const options = {
    // shortLink: "https://mastergo.com/goto/xxxxx",
    // 或者
    // fileId: "file/xxxxx",
    // layerId: "layer_xxxxx",
    pageName: "wallet",
    componentName: "index"
  };
  
  console.log('请提供MasterGo链接来生成实际的DSL代码');
  console.log('示例用法:');
  console.log('node generate-dsl.js --shortLink="https://mastergo.com/goto/xxxxx" --pageName="wallet"');
}
