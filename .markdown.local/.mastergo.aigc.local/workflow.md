# MasterGo DSL 生成工作流程

## 完整工作流程

### 第一步：准备工作

1. **确认DSL类型定义**
   - 已有类型定义文件：`.markdown.local/code-schema.d.ts`
   - 核心类型：`MasterCodeSchema.Dsl`

2. **确认目标页面**
   - 从 `src/pages/` 目录选择目标页面
   - 例如：`wallet`, `vipCenter`, `onlinePayment` 等

3. **准备MasterGo链接**
   - 短链接格式：`https://mastergo.com/goto/xxxxx`
   - 或提供 `fileId` 和 `layerId`

### 第二步：调用MCP工具

使用以下MCP工具获取DSL数据：

```javascript
// 调用MCP工具
const dslResult = await mcp__getDsl_mastergo-magic-mcp({
  shortLink: "https://mastergo.com/goto/xxxxx"
  // 或者
  // fileId: "file/xxxxx",
  // layerId: "layer_xxxxx"
});
```

### 第三步：处理DSL数据

1. **验证DSL结构**
   ```typescript
   interface ExpectedDsl {
     styles: StyleMap;
     nodes: SceneNode[];
     components: ComponentNode[];
   }
   ```

2. **提取组件文档链接**
   ```javascript
   if (dslResult.componentDocumentLinks && dslResult.componentDocumentLinks.length > 0) {
     for (const url of dslResult.componentDocumentLinks) {
       const componentDoc = await mcp__getComponentLink_mastergo-magic-mcp({ url });
       // 处理组件文档
     }
   }
   ```

### 第四步：保存DSL文件

1. **创建目标目录**
   ```
   .markdown.local/.mastergo.aigc.local/pages/{pageName}/{componentName}/
   ```

2. **保存文件**
   - `dsl.json` - DSL数据
   - `dsl.d.ts` - TypeScript定义
   - `README.md` - 文档说明

### 第五步：验证和测试

1. **验证DSL结构**
   - 检查是否符合 `MasterCodeSchema.Dsl` 类型
   - 验证节点层次结构
   - 检查样式定义完整性

2. **生成测试代码**
   - 创建简单的渲染测试
   - 验证组件属性
   - 检查交互逻辑

## 示例：为wallet页面生成DSL

### 输入参数
```javascript
const options = {
  shortLink: "https://mastergo.com/goto/LhGgBAK", // 示例链接
  pageName: "wallet",
  componentName: "index"
};
```

### 执行流程
```javascript
async function generateWalletDSL() {
  try {
    // 1. 调用MCP获取DSL
    const dslData = await mcp__getDsl_mastergo-magic-mcp({
      shortLink: options.shortLink
    });
    
    // 2. 处理组件文档
    if (dslData.componentDocumentLinks) {
      for (const url of dslData.componentDocumentLinks) {
        const componentDoc = await mcp__getComponentLink_mastergo-magic-mcp({ url });
        // 合并组件文档信息
      }
    }
    
    // 3. 创建目标目录
    const targetDir = `.markdown.local/.mastergo.aigc.local/pages/wallet/index/`;
    
    // 4. 保存DSL文件
    await saveDSLFiles(targetDir, dslData);
    
    console.log('DSL生成完成！');
    
  } catch (error) {
    console.error('DSL生成失败:', error);
  }
}
```

## 目录结构映射

根据项目记忆中的规则：

| 源文件路径 | DSL保存路径 |
|-----------|------------|
| `src/pages/wallet/index.tsx` | `.markdown.local/.mastergo.aigc.local/pages/wallet/index/` |
| `src/pages/vipCenter/index.tsx` | `.markdown.local/.mastergo.aigc.local/pages/vipCenter/index/` |
| `src/pages/onlinePayment/index.tsx` | `.markdown.local/.mastergo.aigc.local/pages/onlinePayment/index/` |

## 注意事项

1. **链接有效性**
   - 确保MasterGo链接可访问
   - 检查权限设置

2. **类型兼容性**
   - 生成的DSL必须符合类型定义
   - 注意版本兼容性

3. **文件组织**
   - 保持目录结构一致性
   - 使用统一的命名规范

4. **文档完整性**
   - 包含必要的说明文档
   - 提供使用示例

## 下一步操作

请提供以下信息以开始生成DSL：

1. **MasterGo设计文件链接**
2. **目标页面名称**（从src/pages中选择）
3. **组件名称**（通常为"index"）

提供信息后，我将执行完整的DSL生成流程。
