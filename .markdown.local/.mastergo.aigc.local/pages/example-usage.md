# MasterGo DSL 生成使用指南

## 概述

本文档说明如何根据DSL类型说明文档调用MCP生成图纸的DSL代码，并保存至相应目录。

## DSL类型说明

根据 `.markdown.local/code-schema.d.ts` 文件，DSL的主要类型结构如下：

### 核心类型

```typescript
type Dsl = {
  styles: StyleMap;
  nodes: SceneNode[];
  components: ComponentNode[];
}
```

### 节点类型

- `LayerNode` - 基础图层节点
- `GroupNode` - 分组节点  
- `FrameNode` - 框架节点
- `PathNode` - 路径节点
- `TextNode` - 文本节点
- `ComponentNode` - 组件节点
- `InstanceNode` - 实例节点
- `ComponentSetNode` - 组件集节点
- `EllipseNode` - 椭圆节点

### 样式类型

- `PaintStyle` - 填充样式
- `EffectStyle` - 效果样式
- `FontStyle` - 字体样式

## 使用步骤

### 1. 准备MasterGo链接

需要提供以下格式之一的链接：

```
// 短链接格式
https://mastergo.com/goto/xxxxx

// 或者提供fileId和layerId
fileId: "file/xxxxx"
layerId: "layer_xxxxx"
```

### 2. 调用MCP工具

```typescript
// 使用短链接
mcp__getDsl_mastergo-magic-mcp({
  shortLink: "https://mastergo.com/goto/xxxxx"
})

// 或使用fileId和layerId
mcp__getDsl_mastergo-magic-mcp({
  fileId: "file/xxxxx",
  layerId: "layer_xxxxx"
})
```

### 3. 保存DSL代码

生成的DSL代码将保存到对应的页面目录：

```
.markdown.local/.mastergo.aigc.local/pages/{pageName}/{componentName}/
```

## 目录结构映射

根据项目记忆，目录结构遵循以下规则：

- `src/pages/*.tsx` 文件对应 `.markdown.local/.mastergo.aigc.local/pages/{dirname}/{basename}/`
- 页面文档存储在对应目录的 `README.md` 文件中

## 示例

假设要为 `src/pages/wallet/index.tsx` 页面生成DSL：

1. 目标目录：`.markdown.local/.mastergo.aigc.local/pages/wallet/index/`
2. DSL文件：`dsl.json` 或 `dsl.ts`
3. 文档文件：`README.md`

## 注意事项

1. 确保MasterGo链接有效且可访问
2. 生成的DSL代码需要符合 `MasterCodeSchema.Dsl` 类型定义
3. 保存路径需要与src/pages结构保持一致
4. 如果目录不存在，需要先创建相应目录结构
