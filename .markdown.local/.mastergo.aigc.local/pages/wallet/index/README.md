# wallet/index DSL 文档

## 概述

此目录用于存储从MasterGo设计文件生成的wallet页面DSL代码。

## 目录结构

```
wallet/index/
├── README.md          # 本文档
├── dsl.json          # DSL数据文件（待生成）
└── dsl.d.ts          # TypeScript类型定义（待生成）
```

## 生成DSL的步骤

### 1. 准备MasterGo链接

请提供wallet页面的MasterGo设计文件链接，格式如下：

```
https://mastergo.com/goto/xxxxx
```

或者提供fileId和layerId：

```
fileId: "file/xxxxx"
layerId: "layer_xxxxx"
```

### 2. 调用MCP工具生成DSL

使用以下命令调用MCP工具：

```javascript
// 方式1：使用短链接
await mcp__getDsl_mastergo-magic-mcp({
  shortLink: "https://mastergo.com/goto/xxxxx"
});

// 方式2：使用fileId和layerId
await mcp__getDsl_mastergo-magic-mcp({
  fileId: "file/xxxxx",
  layerId: "layer_xxxxx"
});
```

### 3. 保存DSL数据

生成的DSL数据将符合以下类型定义：

```typescript
type Dsl = {
  styles: StyleMap;
  nodes: SceneNode[];
  components: ComponentNode[];
}
```

## 对应的源文件

此DSL对应的源文件：`src/pages/wallet/index.tsx`

## 预期的DSL结构

根据wallet页面的功能，预期的DSL可能包含：

### 样式定义
- 主色调样式
- 文本样式
- 按钮样式
- 卡片样式

### 节点结构
- 页面容器 (FrameNode)
- 余额显示区域 (FrameNode)
- 交易记录列表 (FrameNode)
- 操作按钮组 (FrameNode)

### 组件定义
- 余额卡片组件
- 交易记录项组件
- 操作按钮组件

## 使用说明

生成DSL后，可以通过以下方式使用：

```typescript
import { dslData } from './dsl.d.ts';

// 渲染DSL数据
function renderWalletPage(dsl: MasterCodeSchema.Dsl) {
  // 根据DSL数据渲染页面
  return dsl.nodes.map(node => renderNode(node));
}
```

## 注意事项

1. 确保MasterGo链接有效且可访问
2. 生成的DSL需要与现有的wallet页面功能保持一致
3. 样式定义应该与项目的设计系统兼容
4. 组件定义应该考虑复用性

## 生成时间

待生成 - 请提供MasterGo链接后执行生成操作
