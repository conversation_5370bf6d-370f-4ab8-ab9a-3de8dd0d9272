{"dsl": {"styles": {"paint_3:07626": {"value": ["#4E5969"], "token": "中性色 Neutral Color/文字 Text/text-4"}, "paint_3:9369": {"value": ["#FFFFFF"], "token": "文字 Text/文字text-1 白色"}, "font_3:07662": {"value": {"family": "PingFangSC-Regular", "size": 16, "decoration": "none", "case": "none", "lineHeight": "140", "letterSpacing": "auto"}, "token": "16/Regular"}}, "nodes": [{"type": "INSTANCE", "id": "450:82279", "layoutStyle": {"width": 375, "height": 812, "relativeX": 0, "relativeY": 0}, "componentInfo": {"name": "提示位置[a4]=中", "properties": {"提示位置": "中"}}, "children": [{"type": "INSTANCE", "id": "450:82279/3:51516", "layoutStyle": {"width": 128, "height": 87, "relativeX": 123.5, "relativeY": 362.5}, "componentInfo": {"name": "类型[a5]=加载,多行文本[a1]=否,垂直[a1]=是", "componentSetDescription": "轻提示", "properties": {"类型": "加载", "多行文本": "否", "垂直": "是", "✍️ 加载文本": "正在提交数据"}}, "children": [{"type": "INSTANCE", "id": "450:82279/3:51516/3:52077", "layoutStyle": {"width": 24, "height": 24, "relativeX": 36, "relativeY": 16}, "componentInfo": {"name": "尺寸[a8]=24", "componentSetDescription": "图标包裹", "properties": {"尺寸": "24"}}, "children": [{"type": "INSTANCE", "id": "450:82279/3:51516/3:52077/3:20002", "layoutStyle": {"width": 24, "height": 24}, "componentInfo": {"name": "刷新-加载-loading"}, "children": [{"type": "PATH", "id": "450:82279/3:51516/3:52077/3:20002/3:07807", "name": "Vector", "layoutStyle": {"width": 40, "height": 40, "relativeX": 4, "relativeY": 4}, "path": [{"fill": "paint_3:07626", "data": "M20 4C11.1634 4 4 11.1634 4 20C4 28.8366 11.1634 36 20 36C28.8366 36 36 28.8366 36 20C36 20 40 20 40 20C40 31.0457 31.0457 40 20 40C8.9543 40 0 31.0457 0 20C0 8.9543 8.9543 0 20 0C20 0 20 4 20 4C20 4 20 4 20 4Z"}]}], "componentId": "3:07806"}], "componentId": "3:20001"}, {"type": "TEXT", "id": "450:82279/3:51516/3:52078", "name": "加载文本", "layoutStyle": {"width": 96, "height": 23, "relativeX": 16, "relativeY": 48}, "text": [{"text": "正在提交数据", "font": "font_3:07662"}], "textColor": [{"start": 0, "end": 6, "color": "paint_3:9369"}], "textAlign": "center", "textMode": "single-line"}], "componentId": "3:52076"}], "componentId": "3:51515"}], "components": []}, "componentDocumentLinks": [], "rules": ["token filed must be generated as a variable (colors, shadows, fonts, etc.) and the token field must be displayed in the comment", "\n            componentDocumentLinks is a list of frontend component documentation links used in the DSL layer, designed to help you understand how to use the components.\n            When it exists and is not empty, you need to use mcp__getComponentLink in a for loop to get the URL content of all components in the list, understand how to use the components, and generate code using the components.\n            For example: \n              ```js  \n                const componentDocumentLinks = [\n                  'https://example.com/ant/button.mdx',\n                  'https://example.com/ant/button.mdx'\n                ]\n                for (const url of componentDocumentLinks) {\n                  const componentLink = await mcp__getComponentLink(url);\n                  console.log(componentLink);\n                }\n              ```\n          "]}