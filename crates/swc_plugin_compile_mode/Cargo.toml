[package]
name = "swc_plugin_compile_mode"
version = "0.1.0"
edition = "2021"

[lib]
crate-type = ["cdylib"]

[dependencies]
serde = { workspace = true }
serde_json = { workspace = true }
swc_core = { workspace = true }
regex = "1.10.2"

[dev-dependencies]
swc_core = { workspace = true, features = ["ecma_parser", "ecma_codegen"] }

# .cargo/config defines few alias to build plugin.
# cargo build-wasi generates wasm-wasi32 binary
# cargo build-wasm32 generates wasm32-unknown-unknown binary.
