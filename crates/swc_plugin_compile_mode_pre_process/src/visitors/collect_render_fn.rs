use std::collections::HashMap;
use swc_core::ecma::{
  ast::*,
  visit::{VisitMut, VisitMutWith},
};

use crate::{
  utils::{constant::COMPILE_MODE, render_fn::RenderFn},
  PluginConfig,
};

pub struct CollectRenderFnVisitor<'a> {
  pub raw_render_fn_map: HashMap<String, RenderFn>,
  sub_component_name: Option<String>,
  sub_component_params: Option<Vec<Pat>>,
  in_outmost_block_scope: bool,
  config: &'a PluginConfig,
}

impl<'a> CollectRenderFnVisitor<'a> {
  pub fn new(config: &'a PluginConfig) -> Self {
    CollectRenderFnVisitor {
      raw_render_fn_map: HashMap::new(),
      sub_component_name: None,
      sub_component_params: None,
      in_outmost_block_scope: true,
      config,
    }
  }
}
//只在最外层找就可以了，因为这个函数是一个 react 组件的入口
impl<'a> VisitMut for CollectRenderFnVisitor<'a> {
  fn visit_mut_block_stmt(&mut self, n: &mut BlockStmt) {
    if !self.in_outmost_block_scope {
      return;
    }
    for stmt in &mut n.stmts {
      match stmt {
        Stmt::Decl(Decl::Fn(fn_decl)) => {
          // 适配 function xxx() {}
          let component_name = fn_decl.ident.sym.to_string();
          // todo 需要调整
          let is_valid_sub_component = component_name.starts_with("render");
          match ((*fn_decl).function.body.as_mut(), is_valid_sub_component) {
            (Some(block_stmt), true) => {
              for stmt in &mut block_stmt.stmts {
                match stmt {
                  Stmt::Return(return_stmt) => {
                    self.in_outmost_block_scope = true;
                    self.sub_component_name = Some(component_name.clone());
                    self.sub_component_params = Some(
                      fn_decl
                        .function
                        .params
                        .clone()
                        .into_iter()
                        .map(|fn_param| fn_param.pat)
                        .collect(),
                    );
                    return_stmt.visit_mut_with(self);
                    self.in_outmost_block_scope = false;
                    self.sub_component_name = None;
                    self.sub_component_params = None;
                  }
                  _ => {}
                }
              }
            }
            _ => {}
          }
        }
        Stmt::Decl(Decl::Var(var_dec)) => {
          for decl in &mut var_dec.decls {
            let sub_component_name = &decl.name;
            match (&mut decl.init, sub_component_name) {
              (Some(expr), Pat::Ident(sub_component_name)) => {
                if let Expr::Arrow(arrow) = &mut **expr {
                  if let BlockStmtOrExpr::BlockStmt(block_stmt) = &mut *arrow.body {
                    for stmt in &mut block_stmt.stmts {
                      match stmt {
                        Stmt::Return(return_stmt) => {
                          self.in_outmost_block_scope = true;
                          self.sub_component_name = Some(sub_component_name.sym.to_string());
                          self.sub_component_params = Some(arrow.params.clone());
                          return_stmt.visit_mut_with(self);
                          self.in_outmost_block_scope = false;
                          self.sub_component_name = None;
                          self.sub_component_params = None;
                        }
                        _ => {}
                      }
                    }
                  }
                }
              }
              _ => {}
            }
          }
        }
        _ => {}
      }
    }
    self.in_outmost_block_scope = false
  }

  fn visit_mut_return_stmt(&mut self, n: &mut ReturnStmt) {
    match (self.in_outmost_block_scope, &n.arg) {
      (true, Some(arg)) => match &**arg {
        Expr::Paren(paren_expr) => {
          if let Expr::JSXElement(jsx_element) = &*paren_expr.expr {
            for attr in &jsx_element.opening.attrs {
              if let JSXAttrOrSpread::JSXAttr(jsx_attr) = attr {
                match (
                  &jsx_attr.name,
                  &jsx_attr.value,
                  &self.sub_component_name,
                  &self.sub_component_params,
                ) {
                  (
                    JSXAttrName::Ident(jsx_attr_name),
                    Some(JSXAttrValue::Lit(Lit::Str(Str { value, .. }))),
                    Some(sub_component_name),
                    Some(sub_component_params),
                  ) => {
                    if jsx_attr_name.sym == COMPILE_MODE && *value == self.config.sub_render_fn {
                      self.raw_render_fn_map.insert(
                        sub_component_name.clone(),
                        RenderFn::new(sub_component_params.clone(), *jsx_element.clone()),
                      );
                    }
                  }
                  _ => {}
                }
              }
            }
          }
        }
        Expr::JSXElement(jsx_element) => {
          for attr in &jsx_element.opening.attrs {
            if let JSXAttrOrSpread::JSXAttr(jsx_attr) = attr {
              match (
                &jsx_attr.name,
                &jsx_attr.value,
                &self.sub_component_name,
                &self.sub_component_params,
              ) {
                (
                  JSXAttrName::Ident(jsx_attr_name),
                  Some(JSXAttrValue::Lit(Lit::Str(Str { value, .. }))),
                  Some(sub_component_name),
                  Some(sub_component_params),
                ) => {
                  if jsx_attr_name.sym == COMPILE_MODE && *value == self.config.sub_render_fn {
                    self.raw_render_fn_map.insert(
                      sub_component_name.clone(),
                      RenderFn::new(sub_component_params.clone(), *jsx_element.clone()),
                    );
                  }
                }
                _ => {}
              }
            }
          }
        }
        _ => {}
      },
      _ => {}
    }
  }
}
