[package]
name = "taro_binding"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
crate-type = ["cdylib"]

[dependencies]
napi = { version = "=2.15.2", features = ["napi4", "tokio_rt"] }
napi-derive = { version = "=2.15.3" }
tokio = { version = "=1.36.0" }
serde_json = { version = "=1.0.114" }
serde = { version = "=1.0.197", features = ["serde_derive"] }
anyhow = { version = "=1.0.80" }

taro_init = { version = "0.0.1" }

[build-dependencies]
napi-build = { version = "=2.1.2" }
