name: Auto reviews
on:
  # When Release Pull Request is merged
  pull_request:
    branches:
      - main
      - 2.x

jobs:
  reviews:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name : GITHUB CONTEXT
        env: 
          GITHUB_CONTEXT: ${{ toJson(github) }} 
        run: echo "$GITHUB_CONTEXT" 
      - name: Get commit message
        run: |
          COMMIT_MESSAGE=$(git log --format=%B -n 1)
          echo "commitmsg=${COMMIT_MESSAGE}" >> $GITHUB_ENV
      - name: Show commit message
        run : echo "$commitmsg"
      - name: Auto reviews branches
        uses: golfzaptw/action-auto-reviews-from-branches@master
        if: contains( env.commitmsg , '[auto review]' ) || contains(github.event.pull_request.title, '[auto review]')
        with:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          EVENT_TYPE: APPROVE
