name: publish
on:
  push:
    branches:
      - 'chore/**'
      - 'feat/**'
      - 'fix/**'
      - '1.x'
      - '2.x'
      - '3.x'
      - '4.x'
  # When Release Pull Request is merged
  pull_request:
    branches:
      - main
    types: [closed]

env:
  CI: true
permissions: {}
jobs:
  need-publish:
    permissions:
      actions: write # to cancel running workflow (andymckay/cancel-action)
    name: Need Publish
    runs-on: ubuntu-latest
    outputs:
      ABORT: ${{ env.ABORT }}
    env:
      ABORT: false
      COMMIT_MESSAGE: ''
    steps:
      # Setup
      - name: Checkout
        uses: actions/checkout@v4

      # Log meta
      - name : GITHUB CONTEXT
        env:
          GITHUB_CONTEXT: ${{ toJson(github) }}
        run: echo "$GITHUB_CONTEXT"

      # Get commit message
      - name: Get commit message
        run: |
          COMMIT_MESSAGE=$(git log --format=%s -n 1)
          echo "COMMIT_MESSAGE=${COMMIT_MESSAGE}" >> $GITHUB_ENV
      - name: Show commit message
        run : echo "$COMMIT_MESSAGE"

      - name: Commit message compliance verification
        if: startsWith( env.COMMIT_MESSAGE , 'chore(release):' ) != true
        run: echo "ABORT=true" >> $GITHUB_ENV

      - name: Publish push tag verification
        if: github.event_name == 'push' && contains( env.COMMIT_MESSAGE , '--tag=' ) != true
        run: echo "ABORT=true" >> $GITHUB_ENV

  build-rust-binding:
    name: Build Rust Binding
    needs: [need-publish]
    if: needs.need-publish.outputs.ABORT != 'true'
    uses: ./.github/workflows/build-rust-binding.yml

  build-rust-wasm:
    name: Build Rust WASM
    needs: [need-publish]
    if: needs.need-publish.outputs.ABORT != 'true'
    uses: ./.github/workflows/build-rust-wasm.yml

  publish:
    permissions:
      contents: write # to create tags and refs
      actions: write # to cancel running workflow (andymckay/cancel-action)
      issues: write # to create comment
      pull-requests: write # to create comment and so on
    name: Publish
    runs-on: ubuntu-latest
    needs: [need-publish, build-rust-binding, build-rust-wasm]
    if: needs.need-publish.outputs.ABORT != 'true'
    env:
      CURRENT_VERSION: ''
      PUBLISH_PARAMS: ''
    steps:
      # Setup
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup pnpm
        uses: pnpm/action-setup@v3.0.0
        with:
          version: 9
      - name: Setup Node 18
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'pnpm'
          registry-url: 'https://registry.npmjs.org' # Don't touch!
      - name: Git Identity
        run: |
          git config --global user.name 'github-actions[bot]'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'
          git remote set-url origin https://x-access-token:${GITHUB_TOKEN}@github.com/$GITHUB_REPOSITORY
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      # Log meta
      - name : GITHUB CONTEXT
        env:
          GITHUB_CONTEXT: ${{ toJson(github) }}
        run: echo "$GITHUB_CONTEXT"

      # Get commit message
      - name: Get commit message
        run: |
          COMMIT_MESSAGE=$(git log --format=%s -n 1)
          echo "COMMIT_MESSAGE=${COMMIT_MESSAGE}" >> $GITHUB_ENV

      # ------------------ If event is push -------------

      # Get & check npm publish
      - name: Get publish params
        if: github.event_name == 'push'
        run: |
          PUBLISH_PARAMS=`echo $COMMIT_MESSAGE | grep -oE "^chore\(release\): publish \S*\s(.*)" | cut -d " " -f 4-`
          echo "PUBLISH_PARAMS=${PUBLISH_PARAMS}" >> $GITHUB_ENV

      - name: Show publish params
        if: github.event_name == 'push'
        run: echo "Publish pushing provide parameter [$PUBLISH_PARAMS]."

      # Define ${CURRENT_VERSION}
      - name: Set Current Version
        shell: bash -ex {0}
        run: |
          CURRENT_VERSION=$(node -p 'require("./package.json").version')
          echo "CURRENT_VERSION=${CURRENT_VERSION}" >> $GITHUB_ENV

      # Check git tag
      - name: Tag Check
        id: tag_check
        shell: bash -ex {0}
        run: |
          GET_API_URL="https://api.github.com/repos/${GITHUB_REPOSITORY}/git/ref/tags/v${CURRENT_VERSION}"
          http_status_code=$(curl -LI $GET_API_URL -o /dev/null -w '%{http_code}\n' -s \
            -H "Authorization: token ${GITHUB_TOKEN}")
          if [ "$http_status_code" -ne "404" ] ; then
            echo "::set-output name=exists_tag::true"
          else
            echo "::set-output name=exists_tag::false"
          fi
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      # ------------------ If git tag already exists, skip -------------

      - name: Git tag verification
        if: steps.tag_check.outputs.exists_tag == false
        uses: andymckay/cancel-action@0.3

      # Bootstrap project
      - name: Install dependencies
        run: pnpm -r install --frozen-lockfile
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: crates/native_binding/artifacts
      - name: Move artifacts
        run: pnpm artifacts
      - name: build
        run: pnpm build
      - name: List Packages
        run: |-
          echo "[Debug] Listing ./crates/native_binding/artifacts"
          ls -R ./crates/native_binding/artifacts
          echo "[Debug] Listing ./npm"
          ls -R ./npm
          echo "[Debug] Listing ./packages/taro-helper/swc"
          ls -R ./packages/taro-helper/swc
        shell: bash

      # Git stash
      - name: Drop current changes
        run: |
          git add .
          git stash

      # Create git tag
      - name: Create Git Tag
        uses: azu/action-package-version-to-git-tag@v2
        with:
          version: ${{ env.CURRENT_VERSION }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          github_repo: ${{ github.repository }}
          git_commit_sha: ${{ github.sha }}
          git_tag_prefix: "v"

      # ------------------ publish -------------

      - name: Publish
        run: |
          if [ '${{ github.event_name }}' == 'pull_request' ] ; then
            pnpm publish --registry=https://registry.npmjs.org/ --publish-branch=${{ github.ref_name }} -r
          elif [ '${{ github.event_name }}' == 'push' ] ; then
            pnpm publish --registry=https://registry.npmjs.org/ --publish-branch=${{ github.ref_name }} -r ${{ env.PUBLISH_PARAMS }}
          fi
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

      # ------------------ After publish -------------

      # Create release when event is PR
      - name: Create Release
        id: create_release
        if: github.event.pull_request.merged == true
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ env.CURRENT_VERSION }}
          # Copy Pull Request's tile and body to Release Note
          release_name: ${{ github.event.pull_request.title }}
          body: |
            ${{ github.event.pull_request.body }}
          draft: false
          prerelease: false

      # Create PR page comment when event is PR
      - uses: actions/github-script@v6
        if: github.event.pull_request.merged == true
        with:
          github-token: ${{secrets.GITHUB_TOKEN}}
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: 'https://github.com/${{ github.repository }}/releases/tag/v${{ env.CURRENT_VERSION }} is released 🎉'
            })
